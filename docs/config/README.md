# Config使用说明

本文档详细介绍 ECS Deep Diagnose 项目的Config管理系统，包括配置能力、YAML 配置规则和使用方法。

## 1. 配置系统提供的能力

### 1.1 核心接口

配置系统提供以下核心接口：

- **`get_config()`** - 获取应用配置，返回 DotDict 对象支持点号访问
- **`get_secret(secret, pub_name)`** - 获取解密后的密钥，集成 KeyCenter
- **`reload_config()`** - 重载配置，清除缓存强制重新加载

### 1.2 配置加载机制

- **多源配置加载**：支持 YAML 文件 + 环境变量的分层配置
- **环境自适应**：根据 `app_env` 环境变量自动选择配置文件
- **配置缓存**：全局配置缓存机制，提高访问性能
- **热重载**：支持运行时重载配置

### 1.3 安全配置管理

- **密钥解密**：集成 KeyCenter，支持加密密钥的自动解密
- **YAML 标签**：支持 `!decrypt` 标签进行密钥解密
- **密钥库管理**：统一管理所有加密密钥

### 1.4 环境管理

- **多环境支持**：daily、pre、pre1、prod
- **环境检测**：自动检测当前运行环境
- **配置文件映射**：不同环境自动加载对应配置文件

### 1.5 DotDict 点号访问

- **点号语法**：支持 `config.app.name` 形式的点号访问
- **嵌套访问**：自动处理嵌套字典的点号访问
- **类型保持**：保持原始数据类型不变

## 2. 配置文件位置

- **开发环境**: `backend/config/files/config_daily.yaml`
- **生产环境**: `backend/config/files/config_prod.yaml`

## 3. Config YAML 配置文件的 Key 定义规则

### 3.1 层级结构规范

配置文件采用层级结构，主要包含以下顶级 key：

```yaml
app:                    # 应用基础配置
  name: string         # 应用名称
  port: number         # 端口号
  home_directory: path # 应用目录
  secret: string       # 应用密钥

workflow:               # 工作流配置
  max_steps: number    # 最大步骤数
  auto_accept_plan: boolean  # 自动接受计划
  enable_background_investigation: boolean  # 启用后台调查

auth:                   # 认证配置
  buc_sso:             # BUC SSO 配置
    host: url          # SSO 主机地址
    app_code: string   # 应用代码
  jwt_users:           # JWT 用户列表
    - user_name: string
      password: string

mcp_servers:            # MCP 服务器配置
  server_name:         # 服务器名称
    protocol: string   # 协议类型
    base_url: url      # 基础 URL
    path: string       # 路径
    token: string      # 认证令牌
    auth: string       # 认证方式
    enabled_tools: []  # 启用的工具列表

security:               # 安全配置
  key_center_public_name: string  # KeyCenter 公钥名
  secrets_vault:        # 密钥库
    key_name: encrypted_value

llm:                    # LLM 配置
  tongyi_provider:      # 通义千问提供商配置
    base_url: url
    api_key: encrypted_string
  profiles:             # LLM 配置文件
    reasoning:          # 推理模型
    basic:              # 基础模型
    vision:             # 视觉模型

observability:          # 可观测性配置
  langfuse:            # Langfuse 配置
    public_key: encrypted_string
    secret_key: encrypted_string
```

### 3.2 特殊 YAML 标签

#### 3.2.1 密钥解密标签 `!decrypt`

```yaml
# 语法：!decrypt [加密密钥引用, 公钥名引用]
api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
```

### 3.3 YAML 锚点和引用

```yaml
security:
  # 定义锚点
  key_center_public_name: &keycenter_pub_name ecs-deep-diagnose_aone_key
  secrets_vault:
    qwen_api_key: &qwen_ak vZnIEeRgVzAzDkDlULclRt6TsbvuG/s4lHnf9PYHb73bo5E2hwCiozUgFBDdecVJ

llm:
  tongyi_provider:
    # 引用锚点
    api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
```


## 4. 其他地方如何查询 Config 中属性

### 4.1 基础用法

```python
from config import get_config

# 获取配置对象
config = get_config()

# 使用点号访问配置项
app_name = config.app.name
port = config.app.port
home_dir = config.app.home_directory
```

### 4.2 访问嵌套配置

```python
# 访问认证配置
buc_host = config.auth.buc_sso.host
app_code = config.auth.buc_sso.app_code

# 访问工作流配置
max_steps = config.workflow.max_steps
auto_accept = config.workflow.auto_accept_plan

# 访问 LLM 配置
reasoning_model = config.llm.profiles.reasoning.model
basic_api_key = config.llm.profiles.basic.api_key
```

### 4.3 访问列表和复杂结构

```python
# 访问 JWT 用户列表
jwt_users = config.auth.jwt_users
for user in jwt_users:
    print(f"用户: {user.user_name}")

# 访问 MCP 服务器配置
vm_coredump_config = config.mcp_servers.vm_coredump
enabled_tools = vm_coredump_config.enabled_tools
```


### 4.4 实际业务代码示例

#### LLM 初始化示例

```python
from config import get_config

def create_llm_client():
    config = get_config()

    # 获取 LLM 配置
    llm_config = config.llm.profiles.basic

    return ChatOpenAI(
        base_url=llm_config.base_url,
        api_key=llm_config.api_key,  # 已自动解密
        model=llm_config.model
    )
```

#### 认证配置示例

```python
from config import get_config

def setup_buc_auth():
    config = get_config()

    # 获取 BUC SSO 配置
    buc_config = config.auth.buc_sso

    return {
        'host': buc_config.host,
        'app_code': buc_config.app_code,
        'app_name': config.app.name
    }
```

#### 工作流配置示例

```python
from config import get_config

def get_workflow_settings():
    config = get_config()

    return {
        'max_steps': config.workflow.max_steps,
        'auto_accept_plan': config.workflow.auto_accept_plan,
        'enable_background_investigation': config.workflow.enable_background_investigation
    }
```

### 4.5 配置重载

```python
from config import reload_config, get_config

# 重载配置（清除缓存）
reload_config()

# 获取最新配置
config = get_config()
```

### 4.6 环境信息访问

```python
# 配置系统自动添加的环境信息
config = get_config()

current_env = config.environment        # 当前环境
is_prod = config.is_production         # 是否生产环境
app_name = config.app_name             # 应用名称
app_env = config.app_env               # 应用环境
```

## 5. 最佳实践

### 5.1 配置访问模式

- **优先使用点号访问**：`config.app.name` 而不是 `config['app']['name']`
- **缓存配置对象**：在模块级别缓存 `get_config()` 结果
- **避免频繁调用**：配置系统已有缓存，但仍建议在模块初始化时获取配置

### 5.2 密钥管理

- **使用 KeyCenter**：所有敏感信息都应通过 KeyCenter 加密
- **YAML 锚点**：使用锚点避免重复定义加密密钥
- **统一密钥库**：在 `security.secrets_vault` 中统一管理所有密钥

### 5.3 环境配置

- **环境隔离**：不同环境使用不同的配置文件
- **环境变量覆盖**：支持环境变量覆盖 YAML 配置
- **配置验证**：在应用启动时验证关键配置项
