"""
Basic import tests to ensure modules can be imported correctly.
"""

import pytest


def test_constants_import():
    """Test that constants module can be imported."""
    try:
        from common.constants import (
            APP_HOME_DIR,
            DEFAULT_MAX_STEP_NUM,
            DEFAULT_AUTO_ACCEPTED_PLAN,
            DEFAULT_ENABLE_BACKGROUND_INVESTIGATION,
            mcp_settings,
            headers
        )
        assert True  # If we get here, imports worked
    except ImportError as e:
        pytest.fail(f"Failed to import constants: {e}")


def test_config_import():
    """Test that config modules can be imported."""
    try:
        from config import BUILT_IN_QUESTIONS, SELECTED_SEARCH_ENGINE, load_yaml_config
        from config import DotDict, Configuration, AGENT_LLM_MAP, SearchEngine, BUILT_IN_QUESTIONS_ZH_CN
        assert True  # If we get here, imports worked
    except ImportError as e:
        pytest.fail(f"Failed to import config modules: {e}")


def test_constants_values():
    """Test basic constants values."""
    from common.constants import (
        APP_HOME_DIR,
        DEFAULT_MAX_STEP_NUM,
        DEFAULT_AUTO_ACCEPTED_PLAN,
        DEFAULT_ENABLE_BACKGROUND_INVESTIGATION
    )
    
    assert isinstance(APP_HOME_DIR, str)
    assert isinstance(DEFAULT_MAX_STEP_NUM, int)
    assert isinstance(DEFAULT_AUTO_ACCEPTED_PLAN, bool)
    assert isinstance(DEFAULT_ENABLE_BACKGROUND_INVESTIGATION, bool)





